* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    background: linear-gradient(
        45deg, 
        rgba(26, 26, 26, 0.8), 
        rgba(44, 62, 80, 0.8)
    ), 
    radial-gradient(
        circle at top right,
        rgba(3, 233, 244, 0.15),
        transparent 50%
    ),
    radial-gradient(
        circle at bottom left,
        rgba(255, 255, 255, 0.1),
        transparent 50%
    );
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.container {
    width: 100%;
    padding: 20px;
}

.login-box {
    background: rgba(255, 255, 255, 0.08);
    padding: 40px 30px;
    border-radius: 15px;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.3);
    max-width: 400px;
    margin: 0 auto;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

h2 {
    color: #fff;
    text-align: center;
    margin-bottom: 40px;
    font-weight: 300;
    letter-spacing: 2px;
}

.form-group {
    position: relative;
    margin-bottom: 30px;
}

input {
    width: 100%;
    padding: 10px 0;
    font-size: 16px;
    color: #fff;
    border: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    outline: none;
    background: transparent;
}

label {
    position: absolute;
    top: 0;
    left: 0;
    padding: 10px 0;
    font-size: 16px;
    color: #fff;
    pointer-events: none;
    transition: 0.5s;
    opacity: 0.7;
}

input:focus ~ label,
input:valid ~ label {
    top: -20px;
    left: 0;
    color: #03e9f4;
    font-size: 12px;
}

.focus-border {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: #03e9f4;
    transition: 0.4s;
}

input:focus ~ .focus-border {
    width: 100%;
}

.code-group {
    display: flex;
    gap: 10px;
}

.code-group input {
    width: 60%;
}

#getCode {
    width: 40%;
    padding: 8px;
    background: rgba(3, 233, 244, 0.1);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(3, 233, 244, 0.5);
    color: #03e9f4;
    border-radius: 5px;
    cursor: pointer;
    transition: 0.3s;
    position: relative;
    overflow: hidden;
}

#getCode::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.8);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1) translate(-50%, -50%);
    transform-origin: 50% 50%;
}

#getCode:active::after {
    animation: ripple 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes ripple {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.8;
    }
    40% {
        transform: translate(-50%, -50%) scale(20);
        opacity: 0.3;
    }
    100% {
        transform: translate(-50%, -50%) scale(40);
        opacity: 0;
    }
}

.login-btn {
    width: 100%;
    padding: 12px;
    background: linear-gradient(145deg, #212121, #2c2c2c);
    border: none;
    border-radius: 25px;
    color: #fff;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 20px;
    position: relative;
    overflow: hidden;
    z-index: 1;
    box-shadow: 5px 5px 10px #1a1a1a, -5px -5px 10px #333333;
}

/* 修改前景波纹效果 */
.login-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.7);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1) translate(-50%, -50%);
    transform-origin: 50% 50%;
}

/* 添加背景波纹效果 */
.login-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 150%;
    padding-top: 150%;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    transform: translate(-50%, -50%) scale(0);
    transition: transform 0.6s ease;
    z-index: -1;
}

/* 悬停时的背景波纹 */
.login-btn:hover::before {
    transform: translate(-50%, -50%) scale(1);
}

/* 点击时的前景波纹 */
.login-btn:active::after {
    animation: ripple 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes ripple {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.8;
    }
    40% {
        transform: translate(-50%, -50%) scale(20);
        opacity: 0.3;
    }
    100% {
        transform: translate(-50%, -50%) scale(40);
        opacity: 0;
    }
}

/* 优化悬停和点击效果 */
.login-btn:hover {
    letter-spacing: 2px;
    color: #03e9f4;
    box-shadow: 0 0 25px rgba(3, 233, 244, 0.5);
    transform: translateY(-2px);
    background: linear-gradient(145deg, #2c2c2c, #212121);
}

.login-btn:active {
    transform: translateY(1px);
    box-shadow: 0 0 15px rgba(3, 233, 244, 0.4);
    transition: all 0.1s ease;
    background: rgba(255, 255, 255, 0.9);
    color: #03e9f4;
}

/* 禁用状态样式 */
.login-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}
 