$(document).ready(function() {
    let countdown = 60;
    let timer = null;

    // 获取验证码
    $('#getCode').click(function() {
        const phone = $('#phone').val();
        
        // 简单的手机号验证
        if(!/^1[3-9]\d{9}$/.test(phone)) {
            alert('请输入正确的手机号码');
            return;
        }

        // 开始倒计时
        const btn = $(this);
        btn.prop('disabled', true);
        timer = setInterval(function() {
            btn.text(`${countdown}秒后重试`);
            countdown--;
            
            if(countdown < 0) {
                clearInterval(timer);
                btn.prop('disabled', false);
                btn.text('获取验证码');
                countdown = 60;
            }
        }, 1000);

        // 这里添加发送验证码的Ajax请求
        // $.ajax({...})
    });

    // 登录按钮点击事件
    $('.login-btn').click(function() {
        const phone = $('#phone').val();
        const code = $('#verifyCode').val();

        if(!phone || !code) {
            alert('请填写完整信息');
            return;
        }

        // 这里添加登录的Ajax请求
        // $.ajax({...})
        
        console.log('登录请求:', {phone, code});
    });
}); 